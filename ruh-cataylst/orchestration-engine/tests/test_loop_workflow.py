#!/usr/bin/env python3
"""
Test script for the 6-step loop workflow implementation.

This test creates a transition schema with:
1. A loop node with entry and exit transitions
2. Two dummy node executor components in the loop body
3. A third node after the loop
4. Tests the complete workflow execution
"""

import asyncio
import json
import logging
from typing import Dict, Any

# Test transition schema for loop workflow
TEST_TRANSITION_SCHEMA = {
    "workflow_id": "test_loop_workflow",
    "workflow_name": "Test Loop Workflow",
    "description": "Test the 6-step loop workflow implementation",
    "transitions": [
        {
            "transition_id": "start_node",
            "transition_name": "Start Node",
            "transition_type": "node_executor",
            "tool_name": "data_preparation",
            "tool_parameters": {
                "operation": "prepare_test_data"
            },
            "next_transitions": ["loop_node"],
            "input_mapping": {},
            "output_mapping": {}
        },
        {
            "transition_id": "loop_node", 
            "transition_name": "Test Loop Node",
            "transition_type": "loop_executor",
            "tool_name": "loop_processor",
            "tool_parameters": {
                "loop_type": "context_preserving",
                "iteration_source": {
                    "type": "list",
                    "data": [
                        {"user_id": "user1", "action": "process", "data": "test_data_1"},
                        {"user_id": "user2", "action": "validate", "data": "test_data_2"},
                        {"user_id": "user3", "action": "transform", "data": "test_data_3"}
                    ]
                },
                "loop_body_configuration": {
                    "entry_transitions": ["dummy_node_1"],
                    "exit_transitions": ["dummy_node_2"],
                    "chain_completion_detection": "explicit_exit_transitions"
                },
                "result_aggregation": {
                    "strategy": "collect_all"
                },
                "error_handling": {
                    "on_iteration_error": "continue"
                }
            },
            "next_transitions": ["final_node"],
            "input_mapping": {},
            "output_mapping": {}
        },
        {
            "transition_id": "dummy_node_1",
            "transition_name": "Dummy Node 1 - Entry",
            "transition_type": "node_executor", 
            "tool_name": "dummy_processor_1",
            "tool_parameters": {
                "operation": "process_entry",
                "simulate_processing": True
            },
            "next_transitions": ["dummy_node_2"],
            "input_mapping": {
                "current_item": "{{current_iteration}}"
            },
            "output_mapping": {}
        },
        {
            "transition_id": "dummy_node_2",
            "transition_name": "Dummy Node 2 - Exit", 
            "transition_type": "node_executor",
            "tool_name": "dummy_processor_2",
            "tool_parameters": {
                "operation": "process_exit",
                "simulate_processing": True
            },
            "next_transitions": [],
            "input_mapping": {
                "processed_data": "{{dummy_node_1.output}}"
            },
            "output_mapping": {}
        },
        {
            "transition_id": "final_node",
            "transition_name": "Final Node",
            "transition_type": "node_executor",
            "tool_name": "final_processor", 
            "tool_parameters": {
                "operation": "finalize_results"
            },
            "next_transitions": [],
            "input_mapping": {
                "loop_results": "{{loop_node.output}}"
            },
            "output_mapping": {}
        }
    ]
}

# Test input data
TEST_INPUT_DATA = {
    "workflow_id": "test_loop_workflow",
    "initial_data": {
        "test_mode": True,
        "source": "loop_workflow_test"
    }
}

async def setup_logging():
    """Setup logging for the test."""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Set specific loggers
    logging.getLogger('orchestration_engine').setLevel(logging.DEBUG)
    logging.getLogger('loop_executor').setLevel(logging.DEBUG)
    logging.getLogger('transition_handler').setLevel(logging.DEBUG)

async def load_test_schema():
    """Load the test schema from test.json file."""
    schema_path = "testing/test.json"
    try:
        with open(schema_path, 'r') as f:
            schema = json.load(f)
        print(f"✅ Test schema loaded from: {schema_path}")
        return schema, schema_path
    except FileNotFoundError:
        print(f"❌ Schema file not found: {schema_path}")
        raise
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in schema file: {e}")
        raise

async def run_loop_workflow_test():
    """
    Main test function to run the loop workflow.
    
    This will:
    1. Load the orchestration engine
    2. Load the test transition schema
    3. Execute the workflow starting from start_node
    4. Monitor the loop execution through all steps
    5. Verify result aggregation
    """
    try:
        print("🚀 Starting Loop Workflow Test")
        print("=" * 50)
        
        # Setup logging
        await setup_logging()

        # Load test schema from test.json
        test_schema, schema_path = await load_test_schema()

        # Import orchestration engine components
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

        from app.core_.state_manager import WorkflowStateManager
        from app.core_.transition_handler import TransitionHandler
        from app.services.loop_executor.loop_executor import LoopExecutor
        
        print("📋 Test Schema Overview:")
        print(f"  - Workflow: {test_schema.get('workflow_name', 'Unknown')}")
        print(f"  - Nodes: {len(test_schema.get('nodes', {}))}")
        print(f"  - Transitions: {len(test_schema.get('transitions', {}))}")
        print()
        
        # Initialize state manager
        print("🔧 Initializing State Manager...")
        state_manager = WorkflowStateManager(workflow_id="test_loop_workflow")

        # Create real components for testing
        from unittest.mock import Mock, AsyncMock
        from aiokafka import AIOKafkaProducer
        from app.services.node_executor import NodeExecutor
        from app.config.config import settings
        import json

        # Mock workflow utils
        workflow_utils = Mock()

        # Mock result callback
        result_callback = AsyncMock()

        # Create real Kafka producer for node executor
        print("🔧 Initializing Kafka Producer...")
        kafka_producer = AIOKafkaProducer(
            bootstrap_servers=settings.kafka_bootstrap_servers,
            value_serializer=lambda v: json.dumps(v).encode("utf-8"),
        )
        await kafka_producer.start()
        print(f"✅ Kafka Producer connected to: {settings.kafka_bootstrap_servers}")

        # Create real node executor
        print("🔧 Initializing Real Node Executor...")
        node_executor = NodeExecutor(producer=kafka_producer)
        await node_executor.start()
        print("✅ Node Executor started and ready for real execution")

        # Create transitions_by_id from schema
        transitions_by_id = {}
        nodes = {}

        # Handle the new schema format with separate nodes and transitions
        if 'transitions' in test_schema:
            if isinstance(test_schema['transitions'], list):
                # Handle list format
                for transition in test_schema['transitions']:
                    transition_id = transition.get('id')
                    if transition_id:
                        transitions_by_id[transition_id] = transition
                        # Create a mock node for each transition
                        nodes[transition_id] = {
                            "id": transition_id,
                            "type": transition.get('execution_type', 'node_executor')
                        }
            elif isinstance(test_schema['transitions'], dict):
                # Handle dict format
                for transition_id, transition in test_schema['transitions'].items():
                    transitions_by_id[transition_id] = transition
                    # Create a mock node for each transition
                    nodes[transition_id] = {
                        "id": transition_id,
                        "type": transition.get('execution_type', 'node_executor')
                    }

        # Also add nodes from the nodes section
        if 'nodes' in test_schema:
            if isinstance(test_schema['nodes'], list):
                # Handle list format
                for node in test_schema['nodes']:
                    node_id = node.get('id')
                    if node_id:
                        nodes[node_id] = node
            elif isinstance(test_schema['nodes'], dict):
                # Handle dict format
                for node_id, node in test_schema['nodes'].items():
                    nodes[node_id] = node

        # Create transition handler with real node executor
        print("🔧 Initializing Transition Handler...")
        transition_handler = TransitionHandler(
            state_manager=state_manager,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            dependency_map={},
            workflow_utils=workflow_utils,
            tool_executor=Mock(),
            node_executor=node_executor,  # Use real node executor
            agent_executor=Mock(),
            result_callback=result_callback,
            user_id="test_user"
        )

        # Create loop executor
        print("🔧 Initializing Loop Executor...")
        loop_executor = LoopExecutor(
            state_manager=state_manager,
            workflow_utils=workflow_utils,
            result_callback=result_callback,
            transitions_by_id=transitions_by_id,
            nodes=nodes,
            transition_handler=transition_handler
        )

        # Set orchestration engine reference (mock)
        mock_orchestration_engine = Mock()
        mock_orchestration_engine.transition_handler = transition_handler
        loop_executor.set_orchestration_engine(mock_orchestration_engine)

        # Get the loop transition (find the LoopNode transition)
        loop_transition_id = None
        loop_transition = None

        for tid, transition in transitions_by_id.items():
            if 'LoopNode' in tid or (transition.get('node_info', {}).get('node_id') == 'LoopNode'):
                loop_transition_id = tid
                loop_transition = transition
                break

        if not loop_transition:
            print("❌ Available transitions:")
            for tid in transitions_by_id.keys():
                print(f"  - {tid}")
            print("\n❌ Available nodes:")
            for nid in nodes.keys():
                print(f"  - {nid}")
            print(f"\n❌ Schema structure:")
            print(f"  - Schema keys: {list(test_schema.keys())}")
            if 'transitions' in test_schema:
                print(f"  - Transitions type: {type(test_schema['transitions'])}")
                if isinstance(test_schema['transitions'], dict):
                    print(f"  - Transition IDs: {list(test_schema['transitions'].keys())}")
            raise Exception("Loop transition not found")

        print(f"🎯 Found loop transition: {loop_transition_id}")
        print("🎯 Starting loop execution...")
        print("=" * 50)

        # Extract loop configuration from transition
        loop_config = loop_transition.get('loop_config', {})

        print(f"🔧 Loop configuration found: {json.dumps(loop_config, indent=2)}")

        if not loop_config:
            print("❌ No loop_config found in transition!")
            return

        # Execute the loop
        result = await loop_executor.execute_tool(
            tool_name="LoopNode",
            tool_parameters=loop_config,
            loop_config=loop_config,
            transition_id=loop_transition_id,
            input_data=TEST_INPUT_DATA
        )
        
        print("=" * 50)
        print("🏁 Workflow Execution Complete")
        print(f"📊 Final Result: {json.dumps(result, indent=2)}")
        
        # Check workflow state for loop results
        print("\n🔍 Checking workflow state for loop execution results...")
        loop_result = state_manager.get_transition_result(loop_transition_id)
        if loop_result:
            print(f"📊 Loop execution result: {json.dumps(loop_result, indent=2)}")
        else:
            print("❌ No loop execution result found")

        # Check all loop states
        all_loop_states = state_manager.get_all_loop_states()
        if all_loop_states:
            print(f"📊 All loop states: {json.dumps(all_loop_states, indent=2)}")
        else:
            print("❌ No loop states found")

        print("=" * 50)
        print("✅ Loop workflow test completed successfully!")

        # Cleanup Kafka resources
        print("🧹 Cleaning up Kafka resources...")
        try:
            await node_executor.stop()
            await kafka_producer.stop()
            print("✅ Kafka resources cleaned up successfully")
        except Exception as cleanup_error:
            print(f"⚠️ Warning: Error during cleanup: {cleanup_error}")

        return result

    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()

        # Cleanup Kafka resources even on error
        print("🧹 Cleaning up Kafka resources after error...")
        try:
            if 'node_executor' in locals():
                await node_executor.stop()
            if 'kafka_producer' in locals():
                await kafka_producer.stop()
            print("✅ Kafka resources cleaned up after error")
        except Exception as cleanup_error:
            print(f"⚠️ Warning: Error during cleanup: {cleanup_error}")

        return None

if __name__ == "__main__":
    print("🧪 Loop Workflow Test Script")
    print("Testing the 6-step loop workflow implementation")
    print()
    
    # Run the test
    result = asyncio.run(run_loop_workflow_test())
    
    if result:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
