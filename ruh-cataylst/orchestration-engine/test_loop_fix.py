#!/usr/bin/env python3
"""
Test script to verify the loop node fix works correctly.
"""

import json
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from services.initialize_workflow import initialize_workflow_with_params

def test_loop_node_fix():
    """Test that the loop node fix correctly processes user input."""
    
    # Create a minimal workflow with a loop transition
    workflow = {
        "transitions": [
            {
                "id": "LoopNode-1750925271398",
                "execution_type": "loop",
                "node_info": {
                    "node_id": "LoopNode",
                    "tools_to_use": [
                        {
                            "tool_id": 1,
                            "tool_name": "LoopNode",
                            "tool_params": {
                                "items": [
                                    {
                                        "field_name": "source_type",
                                        "data_type": "string",
                                        "field_value": "iteration_list"
                                    },
                                    {
                                        "field_name": "iteration_list",
                                        "data_type": "array",
                                        "field_value": []
                                    },
                                    {
                                        "field_name": "batch_size",
                                        "data_type": "string",
                                        "field_value": "1"
                                    }
                                ]
                            }
                        }
                    ]
                },
                "loop_config": {
                    "iteration_behavior": "independent",
                    "iteration_source": {
                        "iteration_list": [],
                        "batch_size": 1
                    },
                    "exit_condition": {
                        "condition_type": "all_items_processed"
                    }
                }
            }
        ]
    }
    
    # Create the user payload (same format as the user provided)
    params = {
        "workflow_id": "b3b7f168-29ee-4e51-8717-16f4c8beb832",
        "approval": True,
        "payload": {
            "user_dependent_fields": [
                "iteration_list"
            ],
            "user_payload_template": {
                "iteration_list": {
                    "value": "[\"HELLO\",\"MNY\",\"EH\"]",
                    "transition_id": "LoopNode-1750925271398"
                }
            }
        }
    }
    
    print("🧪 Testing loop node fix...")
    print(f"📥 Input iteration_list: {params['payload']['user_payload_template']['iteration_list']['value']}")
    
    # Test the fix
    try:
        updated_workflow = initialize_workflow_with_params(workflow, params)
        
        # Check the results
        transition = updated_workflow["transitions"][0]
        
        # Check tool_params was updated
        tool_params_items = transition["node_info"]["tools_to_use"][0]["tool_params"]["items"]
        iteration_list_param = next((item for item in tool_params_items if item["field_name"] == "iteration_list"), None)
        
        print(f"📋 Tool params iteration_list: {iteration_list_param['field_value']}")
        
        # Check loop_config was updated
        loop_config_iteration_list = transition["loop_config"]["iteration_source"]["iteration_list"]
        
        print(f"🔄 Loop config iteration_list: {loop_config_iteration_list}")
        
        # Verify the fix worked
        expected_array = ["HELLO", "MNY", "EH"]

        # Check if tool_params field_value is the expected array (could be list or parsed from JSON string)
        tool_params_value = iteration_list_param["field_value"]
        if isinstance(tool_params_value, str):
            try:
                tool_params_value = json.loads(tool_params_value)
            except:
                pass

        if tool_params_value == expected_array:
            print("✅ Tool params correctly updated!")
        else:
            print(f"❌ Tool params failed: expected {expected_array}, got {tool_params_value} (type: {type(tool_params_value)})")
            return False

        if loop_config_iteration_list == expected_array:
            print("✅ Loop config correctly updated!")
        else:
            print(f"❌ Loop config failed: expected {expected_array}, got {loop_config_iteration_list} (type: {type(loop_config_iteration_list)})")
            return False
            
        print("🎉 All tests passed! The loop node fix is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_loop_node_fix()
    sys.exit(0 if success else 1)
